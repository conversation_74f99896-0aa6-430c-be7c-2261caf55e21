\documentclass{xduugthesis}
\usepackage{booktabs}
\usepackage{multirow}  % 支持表格中的多行合并
\usepackage{amsmath}
\usepackage{amssymb}  % 支持 \mathbb 命令
\usepackage{graphicx}
\usepackage{listings}
\usepackage[backend=biber,style=gb7714-2015]{biblatex} % 添加 biblatex 包


% 盲审版本设置 - 移除致谢部分
\xdusetup{
  style = { latin-font = tac },
  info = {
    title = {基于 MultiVI 的单细胞多组学\\数据集成方法研究及实现},
    author = {解宇钊}, % 隐去作者姓名
    department = {计算机科学与技术学院},
    major = {软件工程},
    class-id = {2103053}, % 隐去班级
    student-id = {21009200233}, % 隐去学号
    supervisor = {鱼亮}, % 隐去导师姓名
    bib-resource = {ref.bib},
    abstract = {chapters/abstract.tex},
    abstract* = {chapters/abstract_en.tex},
    keywords = {单细胞多组学,数据集成,MultiVI,深度学习},
    keywords* = {Single-cell,Multi-omics,Data Integration,MultiVI,Deep Learning},
    acknowledgements = {chapters/acknowledgements.tex}
    }
  }



\begin{document}

\include{chapters/intro}
\include{chapters/theory}
\include{chapters/method}
\include{chapters/experiment}
\include{chapters/conclusion}

\end{document}
